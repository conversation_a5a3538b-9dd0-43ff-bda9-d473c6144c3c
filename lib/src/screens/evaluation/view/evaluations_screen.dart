import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/evaluation/controller/evaluation_controller.dart';
import 'package:connectify_app/src/screens/evaluation/controller/evaluation_tab_bar_controller.dart';
import 'package:connectify_app/src/screens/evaluation/model/evaluation_answer_model.dart';
import 'package:connectify_app/src/screens/evaluation/services/pdf_export_service.dart';
import 'package:connectify_app/src/screens/evaluation/view/add_evaluation_screen.dart';
import 'package:connectify_app/src/screens/evaluation/view/widgets/evaluation_filters.dart';
import 'package:connectify_app/src/screens/evaluation/view/widgets/evaluation_tab_bar_widget.dart';
import 'package:connectify_app/src/screens/evaluation/view/widgets/evaluation_selected_screen.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../home/<USER>/main_screen.dart';

class EvaluationsScreen extends HookConsumerWidget {
  const EvaluationsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedMonth = useState<String?>(null);
    final selectedClass = useState<ClassModel?>(null);
    final evaluationTabBarIndex = ref.watch(evaluationTabBarControllerProvider);

    // Get filtered answers for export (only when on answers tab)
    final filteredAnswersForExport = useMemoized(() {
      if (evaluationTabBarIndex != 0) return <EvaluationAnswerModel>[];

      final evaluationAnswerController =
          ref.read(getEvaluationAnswerDataProvider(context));
      return evaluationAnswerController.when(
        data: (answers) {
          var filtered = answers;

          // Filter by month if selected
          if (selectedMonth.value != null && selectedMonth.value != 'all') {
            filtered = filtered.where((answer) {
              if (answer.createdAt == null) return false;
              final answerMonth =
                  '${answer.createdAt!.year}-${answer.createdAt!.month.toString().padLeft(2, '0')}';
              return answerMonth == selectedMonth.value;
            }).toList();
          }

          // Filter by class if selected
          if (selectedClass.value != null) {
            filtered = filtered.where((answer) {
              return answer.classModel != null &&
                  answer.classModel!.id == selectedClass.value!.id;
            }).toList();
          }

          return filtered;
        },
        loading: () => <EvaluationAnswerModel>[],
        error: (error, stack) => <EvaluationAnswerModel>[],
      );
    }, [evaluationTabBarIndex, selectedMonth.value, selectedClass.value]);

    return WillPopScope(
      onWillPop: () async {
        context.toReplacement(const MainScreen());
        return false;
      },
      child: Scaffold(
        appBar: MainAppBar(
          isBackButton: true,
          title: context.tr.evaluations,
          iconPath: '',
        ),
        body: Column(
          children: [
            // Tab Bar
            const EvaluationTabBarWidget(),

            // Filters Section
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                EvaluationFilters(
                  selectedMonth: selectedMonth,
                  selectedClass: selectedClass,
                ),

                // Reset button if filters exist
                if (selectedMonth.value != null ||
                    selectedClass.value != null) ...[
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppSpaces.mediumPadding,
                      vertical: AppSpaces.smallPadding,
                    ),
                    child: GestureDetector(
                      onTap: () {
                        selectedMonth.value = null;
                        selectedClass.value = null;
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppSpaces.mediumPadding,
                          vertical: AppSpaces.smallPadding,
                        ),
                        decoration: BoxDecoration(
                          color: ColorManager.primaryColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: ColorManager.primaryColor.withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.refresh,
                              size: 16,
                              color: ColorManager.primaryColor,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              context.tr.reset,
                              style: context.labelMedium.copyWith(
                                color: ColorManager.primaryColor,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ],
            ),

            // Show export button only for answers tab
            if (evaluationTabBarIndex == 0)
              Padding(
                padding: const EdgeInsets.all(AppSpaces.mediumPadding),
                child: Button(
                  label: filteredAnswersForExport.length > 100
                      ? '${context.tr.exportReport} (${context.tr.first100})'
                      : context.tr.exportReport,
                  onPressed: () async {
                    try {
                      final answers = filteredAnswersForExport;
                      if (answers.isNotEmpty) {
                        // Show warning for large datasets
                        if (answers.length > 100) {
                          context
                              .showBarMessage(context.tr.largeDatasetDetected);
                        }

                        await PdfExportService.exportEvaluationReport(
                          evaluationAnswers: answers,
                          title: context.tr.evaluationReport,
                        );

                        context
                            .showBarMessage(context.tr.pdfExportedSuccessfully);
                      } else {
                        context.showBarMessage(context.tr.noDataToExport);
                      }
                    } catch (e) {
                      context.showBarMessage(
                          '${context.tr.failedToExportPdf}: $e');
                    }
                  },
                ),
              ),

            context.smallGap,

            // Tab Content
            Expanded(
              child: EvaluationSelectedScreen(
                selectedMonth: selectedMonth.value,
                selectedClassId: selectedClass.value?.id,
              ).paddingAll(AppSpaces.mediumPadding),
            ),
          ],
        ),
        floatingActionButton: const UserModel().isCurrentUserAdmin ||
                hasPermission(const UserModel().currentUser, 'addEvaluations')
            ? FloatingActionButton.extended(
                backgroundColor: ColorManager.primaryColor,
                onPressed: () {
                  context.to(const AddEvaluationScreen());
                },
                label: Text(context.tr.addEvaluation),
                icon: const Icon(Icons.add),
              )
            : null,
      ),
    );
  }
}
