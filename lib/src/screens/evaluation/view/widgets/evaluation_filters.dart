import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/drop_downs/class_drop_down.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:xr_helper/xr_helper.dart';

class EvaluationFilters extends HookWidget {
  final ValueNotifier<String?> selectedMonth;
  final ValueNotifier<ClassModel?> selectedClass;

  const EvaluationFilters({
    super.key,
    required this.selectedMonth,
    required this.selectedClass,
  });

  @override
  Widget build(BuildContext context) {
    // Generate months from January 2025 to current month
    final now = DateTime.now();
    final currentYear = now.year;
    final currentMonth = now.month;

    final months = <Map<String, String>>[];

    // Start from 2025
    for (int year = 2025; year <= currentYear; year++) {
      final startMonth =
          year == 2025 ? 1 : 1; // Always start from January for each year
      final endMonth = year == currentYear
          ? currentMonth
          : 12; // End at current month for current year, December for previous years

      for (int month = startMonth; month <= endMonth; month++) {
        final monthName = _getMonthName(context, month);
        months.add({
          'value': '$year-${month.toString().padLeft(2, '0')}',
          'name': '$monthName $year',
        });
      }
    }

    // Reverse the list so current month appears first
    months.sort((a, b) => b['value']!.compareTo(a['value']!));

    return Container(
      padding: const EdgeInsets.all(AppSpaces.mediumPadding),
      child: Column(
        children: [
          Row(
            children: [
              // Month filter
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.tr.filterByDate,
                      style: context.labelMedium,
                    ),
                    context.smallGap,
                    ValueListenableBuilder<String?>(
                      valueListenable: selectedMonth,
                      builder: (context, month, child) {
                        return BaseSearchDropDown(
                          label: context.tr.selectMonth,
                          data: [
                            {'value': 'all', 'name': context.tr.allMonths},
                            ...months,
                          ],
                          itemModelAsName: (item) =>
                              (item as Map<String, String>)['name']!,
                          selectedValue: months.firstWhere(
                            (m) => m['value'] == month,
                            orElse: () =>
                                {'value': 'all', 'name': context.tr.allMonths},
                          ),
                          isEng: context.isEng,
                          onChanged: (value) {
                            final selected = value as Map<String, String>;
                            selectedMonth.value = selected['value'] == 'all'
                                ? null
                                : selected['value'];
                          },
                        );
                      },
                    ),
                  ],
                ),
              ),

              context.mediumGap,

              // Class filter
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.tr.filterByClass,
                      style: context.labelMedium,
                    ),
                    context.smallGap,
                    ClassDropDown(
                      selectedClass: selectedClass,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _getMonthName(BuildContext context, int month) {
    switch (month) {
      case 1:
        return context.tr.january;
      case 2:
        return context.tr.february;
      case 3:
        return context.tr.march;
      case 4:
        return context.tr.april;
      case 5:
        return context.tr.may;
      case 6:
        return context.tr.june;
      case 7:
        return context.tr.july;
      case 8:
        return context.tr.august;
      case 9:
        return context.tr.september;
      case 10:
        return context.tr.october;
      case 11:
        return context.tr.november;
      case 12:
        return context.tr.december;
      default:
        return '';
    }
  }
}
