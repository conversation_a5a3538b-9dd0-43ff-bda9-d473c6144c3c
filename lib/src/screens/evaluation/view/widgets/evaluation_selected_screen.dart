import 'package:connectify_app/src/screens/evaluation/controller/evaluation_controller.dart';
import 'package:connectify_app/src/screens/evaluation/controller/evaluation_tab_bar_controller.dart';
import 'package:connectify_app/src/screens/evaluation/view/widgets/evaluation_answer_expansion_card.dart';
import 'package:connectify_app/src/screens/evaluation/view/widgets/evaluation_questions_tab_screen.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_list/base_list.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class EvaluationSelectedScreen extends ConsumerWidget {
  final String? selectedMonth;
  final int? selectedClassId;

  const EvaluationSelectedScreen({
    super.key,
    this.selectedMonth,
    this.selectedClassId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final evaluationTabBarIndex = ref.watch(evaluationTabBarControllerProvider);

    switch (evaluationTabBarIndex) {
      case 0:
        // Answers Tab - Load answers data
        final evaluationAnswerController = ref.watch(getEvaluationAnswerDataProvider(context));
        return evaluationAnswerController.get(
          data: (answersData) {
            // Apply filters
            var filteredAnswers = answersData;

            // Filter by month if selected
            if (selectedMonth != null && selectedMonth != 'all') {
              filteredAnswers = filteredAnswers.where((answer) {
                if (answer.createdAt == null) return false;
                final answerMonth =
                    '${answer.createdAt!.year}-${answer.createdAt!.month.toString().padLeft(2, '0')}';
                return answerMonth == selectedMonth;
              }).toList();
            }

            // Filter by class if selected
            if (selectedClassId != null) {
              filteredAnswers = filteredAnswers.where((answer) {
                return answer.classModel != null &&
                    answer.classModel!.id == selectedClassId;
              }).toList();
            }

            return BaseList(
              data: filteredAnswers,
              separatorGap: context.mediumGap,
              itemBuilder: (evaluationAnswer, index) {
                return EvaluationAnswerExpansionCard(
                  evaluationAnswer: evaluationAnswer,
                );
              },
              mainAxisSpacing: 15.h,
              crossAxisSpacing: 10,
              emptyWidget: Center(
                child: Text(
                  context.tr.noEvaluationAnswers,
                  style: context.subHeadLine,
                ),
              ),
            );
          },
          error: (error, stack) => Center(
            child: Text(
              '${context.tr.errorLoadingAnswers}: $error',
              style: context.subHeadLine,
            ),
          ),
        );
      case 1:
        // Questions Tab - Load questions data
        final evaluationDataController = ref.watch(getEvaluationDataProvider(context));
        return evaluationDataController.get(
          data: (evaluationsData) {
            // Apply filters
            var filteredEvaluations = evaluationsData;

            // Filter by month if selected
            if (selectedMonth != null && selectedMonth != 'all') {
              filteredEvaluations = filteredEvaluations.where((evaluation) {
                if (evaluation.createdAt == null) return false;
                final evaluationMonth =
                    '${evaluation.createdAt!.year}-${evaluation.createdAt!.month.toString().padLeft(2, '0')}';
                return evaluationMonth == selectedMonth;
              }).toList();
            }

            // Filter by class if selected
            if (selectedClassId != null) {
              filteredEvaluations = filteredEvaluations.where((evaluation) {
                return evaluation.classes != null &&
                    evaluation.classes!.any((cls) => cls.id == selectedClassId);
              }).toList();
            }

            return EvaluationQuestionsTabScreen(
              evaluationsData: filteredEvaluations,
            );
          },
          error: (error, stack) => Center(
            child: Text(
              '${context.tr.errorLoadingEvaluations}: $error',
              style: context.subHeadLine,
            ),
          ),
        );
    }
    return const SizedBox.shrink();
  }
}
