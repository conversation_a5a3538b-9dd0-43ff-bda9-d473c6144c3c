import 'package:connectify_app/src/screens/evaluation/model/evaluation_answer_model.dart';
import 'package:connectify_app/src/screens/evaluation/model/evaluation_question_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:xr_helper/xr_helper.dart';

class EvaluationAnswerCard extends StatelessWidget {
  final EvaluationAnswerModel evaluationAnswer;

  const EvaluationAnswerCard({
    super.key,
    required this.evaluationAnswer,
  });

  @override
  Widget build(BuildContext context) {
    return BaseContainer(
      margin: AppSpaces.smallPadding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with parent name, student name, and date
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      evaluationAnswer.parent?.name ??
                          evaluationAnswer.studentName,
                      style: context.boldTitle,
                    ),
                    if (evaluationAnswer.classModel != null) ...[
                      context.smallGap,
                      Text(
                        '${context.tr.className}: ${evaluationAnswer.classModel!.name}',
                        style: context.greyLabelMedium,
                      ),
                    ],
                  ],
                ),
              ),
              if (evaluationAnswer.createdAt != null)
                Text(
                  evaluationAnswer.createdAt!.formatDateToString,
                  style: context.smallHint,
                ),
            ],
          ),

          context.mediumGap,

          // Divider
          const Divider(
            thickness: 1,
            color: ColorManager.grey,
          ),

          context.smallGap,

          // Questions and answers
          ...evaluationAnswer.answers.map((questionAnswer) {
            return _buildQuestionAnswerItem(context, questionAnswer);
          }),
        ],
      ),
    );
  }

  Widget _buildQuestionAnswerItem(
      BuildContext context, EvaluationQuestionModel questionAnswer) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSpaces.mediumPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Question
          Text(
            questionAnswer.question,
            style: context.subTitle.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),

          context.smallGap,

          // Answer based on type
          if (questionAnswer.type == QuestionType.rating &&
              questionAnswer.rate != null)
            _buildRatingAnswer(context, questionAnswer.rate!)
          else if (questionAnswer.type == QuestionType.text &&
              questionAnswer.answer != null)
            _buildTextAnswer(context, questionAnswer.answer!),
        ],
      ),
    );
  }

  Widget _buildRatingAnswer(BuildContext context, double rating) {
    return RatingBar.builder(
      initialRating: rating,
      minRating: 1,
      direction: Axis.horizontal,
      allowHalfRating: true,
      ignoreGestures: true,
      // Read-only
      itemCount: 5,
      itemSize: 30,
      itemPadding: const EdgeInsets.symmetric(horizontal: 2.0),
      itemBuilder: (context, _) => const Icon(
        Icons.star,
        color: Colors.amber,
      ),
      onRatingUpdate: (rating) {
        // Do nothing - read-only
      },
    );
  }

  Widget _buildTextAnswer(BuildContext context, String answer) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppSpaces.mediumPadding),
      decoration: BoxDecoration(
        color: ColorManager.greyColor,
        borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
      ),
      child: Text(
        answer,
        style: context.body,
      ),
    );
  }
}
