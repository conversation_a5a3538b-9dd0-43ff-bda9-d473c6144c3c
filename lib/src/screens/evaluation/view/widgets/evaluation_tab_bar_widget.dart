import 'package:connectify_app/src/screens/evaluation/controller/evaluation_tab_bar_controller.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class EvaluationTabBarWidget extends ConsumerWidget {
  const EvaluationTabBarWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final evaluationTabBarCtrl = ref.watch(evaluationTabBarController);
    final evaluationChangeNotifierCtrl =
        ref.watch(evaluationTabBarControllerProvider);

    final List<String> tabs = [
      context.tr.answers,
      context.tr.questions,
    ];

    return DefaultTabController(
      initialIndex: evaluationChangeNotifierCtrl,
      length: tabs.length,
      child: TabBar(
        indicatorSize: TabBarIndicatorSize.label,
        tabAlignment: TabAlignment.center,
        onTap: (index) {
          evaluationTabBarCtrl.changeIndex(index);
        },
        indicatorColor: Colors.transparent,
        indicator: const BoxDecoration(
          color: ColorManager.primaryColor,
          borderRadius: BorderRadius.all(Radius.circular(10)),
        ),
        dividerColor: ColorManager.grey,
        dividerHeight: 0,
        unselectedLabelColor: ColorManager.darkGrey.withOpacity(0.5),
        isScrollable: true,
        tabs: tabs
            .map((e) => ClipRRect(
                  borderRadius: BorderRadius.circular(10),
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: context.width * 0.03,
                      vertical: 9.h,
                    ),
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      alignment: Alignment.center,
                      child: Text(
                        e,
                        style: evaluationChangeNotifierCtrl == tabs.indexOf(e)
                            ? context.whiteLabelLarge
                            : context.hint,
                      ),
                    ),
                  ),
                ))
            .toList(),
      ),
    ).paddingOnly(
      top: AppSpaces.mediumPadding,
    );
  }
}
