import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/evaluation/controller/evaluation_controller.dart';
import 'package:connectify_app/src/screens/evaluation/model/evaluation_model.dart';
import 'package:connectify_app/src/screens/evaluation/model/evaluation_question_model.dart';
import 'package:connectify_app/src/screens/evaluation/view/widgets/evaluation_fields.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class AddEvaluationScreen extends HookConsumerWidget {
  final EvaluationModel? evaluation;

  const AddEvaluationScreen({super.key, this.evaluation});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final evaluationCtrl =
        ref.watch(evaluationChangeNotifierControllerProvider(context));

    final formKey = useState(GlobalKey<FormState>());

    // Generate default month (current year, first month)
    final currentYear = DateTime.now().year;
    final defaultMonth = '$currentYear-01'; // January of current year

    // Form state
    final selectedMonth = useState<String?>(evaluation?.date ?? defaultMonth);
    final selectedSendTo =
        useState<SendTo>(evaluation?.sendTo ?? SendTo.allParents);
    final selectedClasses = useState<List<ClassModel>?>(evaluation?.classes);

    // Questions state
    final questions = useState<List<EvaluationQuestionModel>>(
        evaluation?.questions ?? [const EvaluationQuestionModel()]);

    Future<void> handleSubmit() async {
      if (formKey.value.currentState?.validate() ?? false) {
        if (selectedMonth.value == null) {
          context.showBarMessage('Please select a month');
          return;
        }

        if (selectedSendTo.value == SendTo.classes &&
            (selectedClasses.value?.isEmpty ?? true)) {
          context.showBarMessage('Please select at least one class');
          return;
        }

        final evaluationModel = EvaluationModel(
          id: evaluation?.id,
          date: selectedMonth.value!,
          sendTo: selectedSendTo.value,
          classes: selectedSendTo.value == SendTo.classes
              ? selectedClasses.value
              : null,
          questions:
              questions.value.where((q) => q.question.isNotEmpty).toList(),
        );

        if (evaluation != null) {
          await evaluationCtrl.editEvaluation(
            id: evaluation!.id!,
            evaluation: evaluationModel,
          );
        } else {
          await evaluationCtrl.addEvaluation(
            evaluation: evaluationModel,
          );
        }
      }
    }

    return Scaffold(
      appBar: MainAppBar(
        isBackButton: true,
        title:
            evaluation != null ? 'Edit Evaluation' : context.tr.addEvaluation,
        iconPath: '',
      ),
      body: Padding(
        padding: const EdgeInsets.all(AppSpaces.mediumPadding),
        child: Form(
          key: formKey.value,
          child: Column(
            children: [
              Expanded(
                child: EvaluationFields(
                  selectedMonth: selectedMonth,
                  selectedSendTo: selectedSendTo,
                  selectedClasses: selectedClasses,
                  questions: questions,
                ),
              ),
              context.largeGap,
              Button(
                isLoading: evaluationCtrl.isLoading,
                loadingWidget: const LoadingWidget(),
                onPressed: handleSubmit,
                label: evaluation != null ? context.tr.update : context.tr.add,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
